"""
日志工具模块 - 优化版本，解决重复日志问题
"""
import logging
import os
from config_v3 import ConfigV3

# 全局标志，确保只初始化一次
_logger_initialized = False

def setup_logger():
    """设置应用日志 - 防止重复初始化"""
    global _logger_initialized

    # 如果已经初始化过，直接返回根日志器
    if _logger_initialized:
        return logging.getLogger()

    # 创建日志目录
    log_dir = os.path.dirname(ConfigV3.LOG_FILE) if os.path.dirname(ConfigV3.LOG_FILE) else "."
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 获取根日志器
    root_logger = logging.getLogger()

    # 清除现有的处理器（防止重复添加）
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置日志级别
    root_logger.setLevel(getattr(logging, ConfigV3.LOG_LEVEL))

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建文件处理器
    file_handler = logging.FileHandler(ConfigV3.LOG_FILE, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(getattr(logging, ConfigV3.LOG_LEVEL))

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(getattr(logging, ConfigV3.LOG_LEVEL))

    # 添加处理器到根日志器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # 防止日志传播到父日志器（避免重复）
    root_logger.propagate = False

    # 设置第三方库的日志级别，减少噪音
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

    # 设置PandasAI相关日志级别
    logging.getLogger('pandasai').setLevel(logging.INFO)
    logging.getLogger('litellm').setLevel(logging.WARNING)

    # 标记为已初始化
    _logger_initialized = True

    return root_logger

def get_logger(name):
    """获取指定名称的日志器"""
    # 确保日志系统已初始化
    if not _logger_initialized:
        setup_logger()

    logger = logging.getLogger(name)
    # 防止子日志器重复输出
    logger.propagate = True
    return logger

def reset_logger():
    """重置日志器（主要用于测试）"""
    global _logger_initialized
    _logger_initialized = False

    # 清除所有处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 重置日志级别
    root_logger.setLevel(logging.WARNING)
