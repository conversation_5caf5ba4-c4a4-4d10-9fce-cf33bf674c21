#!/usr/bin/env python3
"""
阿里云DashScope API配置测试
专门测试中国大陆地区的DashScope接口配置
"""
import os
import pandas as pd
from dotenv import load_dotenv

def test_dashscope_api_config():
    """测试DashScope API配置"""
    print("🔧 测试阿里云DashScope API配置")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        return False
    
    print(f"✅ API密钥已配置: {api_key[:10]}...")
    
    # 设置DashScope环境变量
    os.environ['DASHSCOPE_API_KEY'] = api_key
    os.environ['DASHSCOPE_BASE_URL'] = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
    
    print(f"✅ DashScope端点: {os.environ['DASHSCOPE_BASE_URL']}")
    
    try:
        # 导入LiteLLM
        from pandasai_litellm.litellm import LiteLLM
        print("✅ LiteLLM导入成功")
        
        # 创建LiteLLM实例 - 使用正确的DashScope配置
        llm = LiteLLM(
            model="dashscope/qwen-max",  # 确保使用dashscope前缀
            temperature=0.1,
            max_tokens=1000,
            api_key=api_key,
            api_base='https://dashscope.aliyuncs.com/compatible-mode/v1'
        )
        print("✅ LiteLLM实例创建成功")
        print(f"   模型: dashscope/qwen-max")
        print(f"   端点: https://dashscope.aliyuncs.com/compatible-mode/v1")
        
        return True
        
    except Exception as e:
        print(f"❌ LiteLLM配置失败: {e}")
        return False

def test_pandasai_with_dashscope():
    """测试PandasAI与DashScope的集成"""
    print("\n🤖 测试PandasAI与DashScope集成")
    print("=" * 60)
    
    try:
        import pandasai as pai
        from pandasai_litellm.litellm import LiteLLM
        
        # 加载环境变量
        load_dotenv()
        api_key = os.getenv('DASHSCOPE_API_KEY')
        
        if not api_key:
            print("⚠️ 跳过集成测试：未找到API密钥")
            return True
        
        # 设置环境变量
        os.environ['DASHSCOPE_API_KEY'] = api_key
        os.environ['DASHSCOPE_BASE_URL'] = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        
        # 创建LiteLLM实例
        llm = LiteLLM(
            model="dashscope/qwen-max",
            temperature=0.1,
            max_tokens=500,
            api_key=api_key,
            api_base='https://dashscope.aliyuncs.com/compatible-mode/v1'
        )
        
        # 配置PandasAI
        pai.config.set({
            "llm": llm,
            "save_logs": True,
            "verbose": False,
            "max_retries": 3,
            "enable_cache": True,
            "seed": 42
        })
        print("✅ PandasAI配置成功")
        
        # 创建测试数据
        df = pd.DataFrame({
            'name': ['张三', '李四', '王五'],
            'age': [25, 30, 35],
            'score': [85, 90, 88]
        })
        
        # 创建PandasAI DataFrame
        pai_df = pai.DataFrame(df)
        print("✅ PandasAI DataFrame创建成功")
        
        # 测试简单查询
        print("\n🔍 测试API调用...")
        question = "这个数据有多少行？"
        print(f"问题: {question}")
        
        try:
            result = pai_df.chat(question)
            print(f"✅ API调用成功！")
            print(f"结果: {result}")
            return True
            
        except Exception as chat_error:
            print(f"❌ API调用失败: {chat_error}")
            
            # 检查是否是认证错误
            if "AuthenticationError" in str(chat_error):
                print("🔍 认证错误分析:")
                print("   - 请检查DASHSCOPE_API_KEY是否正确")
                print("   - 请确认API密钥有效且未过期")
                print("   - 请验证账户余额是否充足")
            
            # 检查是否是网络错误
            elif "ConnectionError" in str(chat_error) or "timeout" in str(chat_error).lower():
                print("🔍 网络错误分析:")
                print("   - 请检查网络连接")
                print("   - 请确认防火墙设置")
                print("   - 请验证DashScope端点可访问性")
            
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_api_call():
    """直接测试DashScope API调用"""
    print("\n🌐 直接测试DashScope API")
    print("=" * 60)
    
    try:
        import requests
        import json
        
        # 加载环境变量
        load_dotenv()
        api_key = os.getenv('DASHSCOPE_API_KEY')
        
        if not api_key:
            print("⚠️ 跳过直接API测试：未找到API密钥")
            return True
        
        # DashScope API端点
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-max",
            "messages": [
                {"role": "user", "content": "你好，请回答：1+1等于多少？"}
            ],
            "temperature": 0.1,
            "max_tokens": 100
        }
        
        print(f"🔗 API端点: {url}")
        print(f"🔑 API密钥: {api_key[:10]}...")
        print(f"📝 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 直接API调用成功！")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 直接API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 阿里云DashScope配置全面测试")
    print("=" * 80)
    
    tests = [
        ("API配置测试", test_dashscope_api_config),
        ("PandasAI集成测试", test_pandasai_with_dashscope),
        ("直接API调用测试", test_direct_api_call)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！DashScope配置正确。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        print("\n🔧 故障排除建议:")
        print("1. 确认DASHSCOPE_API_KEY环境变量正确设置")
        print("2. 验证API密钥有效性和账户余额")
        print("3. 检查网络连接和防火墙设置")
        print("4. 确认使用的是中国大陆版DashScope端点")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
