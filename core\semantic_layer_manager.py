"""
语义层管理器 - PandasAI v3 语义层高级功能
基于官方文档: https://docs.pandas-ai.com/v3/semantic-layer
"""
import os
import yaml
import json
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import shutil
from datetime import datetime

from config_v3 import ConfigV3
from utils.logger import get_logger

logger = get_logger(__name__)

class SemanticLayerManager:
    """语义层管理器 - 处理Schema创建、验证、更新等高级功能"""
    
    def __init__(self):
        self.base_path = Path(ConfigV3.SEMANTIC_LAYER_PATH)
        self.financial_path = self.base_path / "financial"
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        self.financial_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"语义层目录已准备: {self.financial_path}")
    
    def create_schema_file(self, dataset_name: str, df: pd.DataFrame, 
                          dataset_type: str = "general", 
                          custom_description: str = None) -> Tuple[bool, str]:
        """
        创建Schema文件
        返回: (是否成功, 消息)
        """
        try:
            # 验证数据集名称
            safe_name = ConfigV3.validate_dataset_name(dataset_name)
            dataset_dir = self.financial_path / safe_name
            
            # 创建数据集目录
            dataset_dir.mkdir(exist_ok=True)
            
            # 生成Schema数据
            schema_data = self._generate_schema_data(
                safe_name, df, dataset_type, custom_description
            )
            
            # 验证Schema
            is_valid, errors = ConfigV3.validate_schema(schema_data)
            if not is_valid:
                return False, f"Schema验证失败: {'; '.join(errors)}"
            
            # 保存Schema文件
            schema_file = dataset_dir / "schema.yaml"
            with open(schema_file, 'w', encoding='utf-8') as f:
                yaml.dump(schema_data, f, default_flow_style=False, 
                         allow_unicode=True, sort_keys=False)
            
            # 保存数据文件
            data_file = dataset_dir / "data.parquet"
            df.to_parquet(data_file, index=False)
            
            # 创建元数据文件
            metadata = self._generate_metadata(safe_name, df, dataset_type)
            metadata_file = dataset_dir / "metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"语义数据集创建成功: {safe_name}")
            return True, f"语义数据集 '{safe_name}' 创建成功"
            
        except Exception as e:
            error_msg = f"创建语义数据集失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def _generate_schema_data(self, name: str, df: pd.DataFrame, 
                             dataset_type: str, custom_description: str = None) -> Dict:
        """生成Schema数据结构"""
        schema_info = ConfigV3.get_financial_schema(dataset_type)
        
        # 生成列定义
        columns = []
        for col in df.columns:
            dtype = str(df[col].dtype)
            col_type = ConfigV3.get_data_type_mapping(dtype)
            
            # 特殊处理日期类型
            if 'date' in col.lower() or 'time' in col.lower():
                col_type = "datetime"
            
            # 生成描述
            description_cn = self._generate_smart_description(col, dataset_type, df[col])
            
            column_def = {
                "name": col,
                "type": col_type,
                "description": description_cn
            }
            
            # 添加约束条件
            constraints = self._generate_column_constraints(df[col], col_type)
            if constraints:
                column_def["constraints"] = constraints
            
            columns.append(column_def)
        
        # 构建Schema数据
        schema_data = {
            "name": name,
            "source": {
                "type": "parquet",
                "path": "data.parquet"
            },
            "description": custom_description or schema_info["description"],
            "columns": columns
        }
        
        # 添加Views配置（如果有）
        if "views" in schema_info and schema_info["views"]:
            schema_data["views"] = schema_info["views"]
        
        # 添加转换配置（如果有）
        if "transformations" in schema_info and schema_info["transformations"]:
            schema_data["transformations"] = schema_info["transformations"]
        
        return schema_data
    
    def _generate_smart_description(self, column_name: str, dataset_type: str, 
                                   series: pd.Series) -> str:
        """生成智能列描述"""
        col_lower = column_name.lower()
        
        # 基础描述
        base_desc = self._get_base_description(col_lower, dataset_type)
        
        # 添加数据特征描述
        if series.dtype in ['int64', 'float64']:
            min_val = series.min()
            max_val = series.max()
            base_desc += f" (范围: {min_val} - {max_val})"
        elif series.dtype == 'object':
            unique_count = series.nunique()
            if unique_count <= 10:
                unique_values = series.unique()[:5]
                base_desc += f" (包含: {', '.join(map(str, unique_values))}等)"
            else:
                base_desc += f" ({unique_count}个不同值)"
        
        return base_desc
    
    def _get_base_description(self, col_lower: str, dataset_type: str) -> str:
        """获取基础描述"""
        # 财务关键词映射
        keywords = {
            'date': '日期字段', 'time': '时间字段', 'amount': '金额字段',
            'revenue': '收入字段', 'income': '收入字段', 'expense': '支出字段',
            'cost': '成本字段', 'profit': '利润字段', 'category': '类别字段',
            'region': '地区字段', 'department': '部门字段', 'product': '产品字段'
        }
        
        for keyword, desc in keywords.items():
            if keyword in col_lower:
                return desc
        
        # 默认描述
        type_desc = {
            "revenue": "收入相关字段",
            "expenses": "支出相关字段", 
            "balance_sheet": "资产负债表字段",
            "general": "数据字段"
        }
        
        return type_desc.get(dataset_type, "数据字段")
    
    def _generate_column_constraints(self, series: pd.Series, col_type: str) -> Optional[Dict]:
        """生成列约束条件"""
        constraints = {}
        
        # 非空约束
        null_count = series.isnull().sum()
        if null_count == 0:
            constraints["nullable"] = False
        
        # 数值约束
        if col_type in ["integer", "float"] and series.notna().any():
            constraints["min"] = float(series.min())
            constraints["max"] = float(series.max())
        
        # 字符串长度约束
        if col_type == "string" and series.notna().any():
            str_lengths = series.astype(str).str.len()
            constraints["min_length"] = int(str_lengths.min())
            constraints["max_length"] = int(str_lengths.max())
        
        return constraints if constraints else None
    
    def _generate_metadata(self, name: str, df: pd.DataFrame, dataset_type: str) -> Dict:
        """生成元数据"""
        return {
            "name": name,
            "type": dataset_type,
            "created_at": datetime.now().isoformat(),
            "rows": len(df),
            "columns": len(df.columns),
            "size_mb": round(df.memory_usage(deep=True).sum() / 1024 / 1024, 2),
            "column_names": df.columns.tolist(),
            "data_types": df.dtypes.astype(str).to_dict(),
            "null_counts": df.isnull().sum().to_dict(),
            "unique_counts": df.nunique().to_dict()
        }
    
    def list_datasets(self) -> List[Dict]:
        """列出所有语义数据集"""
        datasets = []
        
        if not self.financial_path.exists():
            return datasets
        
        for dataset_dir in self.financial_path.iterdir():
            if dataset_dir.is_dir():
                schema_file = dataset_dir / "schema.yaml"
                metadata_file = dataset_dir / "metadata.json"
                
                if schema_file.exists():
                    try:
                        with open(schema_file, 'r', encoding='utf-8') as f:
                            schema = yaml.safe_load(f)
                        
                        metadata = {}
                        if metadata_file.exists():
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                        
                        datasets.append({
                            "name": schema.get("name", dataset_dir.name),
                            "description": schema.get("description", ""),
                            "columns": len(schema.get("columns", [])),
                            "path": str(dataset_dir),
                            "metadata": metadata
                        })
                    except Exception as e:
                        logger.warning(f"读取数据集 {dataset_dir.name} 失败: {e}")
        
        return datasets
    
    def delete_dataset(self, dataset_name: str) -> Tuple[bool, str]:
        """删除语义数据集"""
        try:
            safe_name = ConfigV3.validate_dataset_name(dataset_name)
            dataset_dir = self.financial_path / safe_name
            
            if not dataset_dir.exists():
                return False, f"数据集 '{safe_name}' 不存在"
            
            shutil.rmtree(dataset_dir)
            logger.info(f"数据集 '{safe_name}' 已删除")
            return True, f"数据集 '{safe_name}' 删除成功"
            
        except Exception as e:
            error_msg = f"删除数据集失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_dataset_info(self, dataset_name: str) -> Optional[Dict]:
        """获取数据集详细信息"""
        try:
            safe_name = ConfigV3.validate_dataset_name(dataset_name)
            dataset_dir = self.financial_path / safe_name
            
            if not dataset_dir.exists():
                return None
            
            schema_file = dataset_dir / "schema.yaml"
            metadata_file = dataset_dir / "metadata.json"
            
            info = {}
            
            if schema_file.exists():
                with open(schema_file, 'r', encoding='utf-8') as f:
                    info["schema"] = yaml.safe_load(f)
            
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    info["metadata"] = json.load(f)
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据集信息失败: {e}")
            return None
