"""
PandasAI v3 分析引擎 - 严格遵循官方API规范
基于官方文档: https://docs.pandas-ai.com/v3/introduction
"""
import pandas as pd
import pandasai as pai
from pandasai_litellm.litellm import LiteLLM
from typing import Any, Dict, List, Optional, Tuple
import os
import shutil
from config_v3 import ConfigV3
from utils.logger import get_logger
from core.semantic_layer_manager import SemanticLayerManager

logger = get_logger(__name__)

class AnalysisEngineV3:
    """
    PandasAI v3 分析引擎
    严格遵循官方文档API规范: https://docs.pandas-ai.com/v3/
    """
    
    def __init__(self):
        self.current_data = None
        self.semantic_datasets = {}
        self.llm_configured = False
        self.semantic_manager = SemanticLayerManager()
        self._setup_llm()
    
    def _setup_llm(self):
        """
        设置LLM配置 - 基于官方文档 /v3/large-language-models
        使用LiteLLM统一接口支持阿里云千问
        """
        try:
            # 验证API密钥
            if not ConfigV3.DASHSCOPE_API_KEY:
                logger.warning("DASHSCOPE_API_KEY未设置，将使用备用模式")
                self.llm_configured = False
                return

            # 设置环境变量供LiteLLM使用
            os.environ['DASHSCOPE_API_KEY'] = ConfigV3.DASHSCOPE_API_KEY

            # 配置LiteLLM使用国内版DashScope端点
            os.environ['DASHSCOPE_BASE_URL'] = 'https://dashscope.aliyuncs.com/compatible-mode/v1'

            # 确保模型名称格式正确
            model_name = ConfigV3.LLM_MODEL
            if not model_name.startswith('dashscope/'):
                model_name = f"dashscope/{model_name}"

            logger.info(f"正在配置LLM模型: {model_name}")
            logger.info(f"使用DashScope端点: https://dashscope.aliyuncs.com/compatible-mode/v1")

            # 创建LiteLLM实例 - 官方推荐方式
            llm = LiteLLM(
                model=model_name,
                temperature=ConfigV3.LLM_TEMPERATURE,
                max_tokens=ConfigV3.LLM_MAX_TOKENS,
                api_key=ConfigV3.DASHSCOPE_API_KEY,  # 显式传递API密钥
                api_base='https://dashscope.aliyuncs.com/compatible-mode/v1'  # 使用国内版端点
            )

            # 全局配置PandasAI - 基于官方文档 /v3/overview-nl#configure-the-nl-layer
            config_dict = {
                "llm": llm,
                **ConfigV3.PANDASAI_CONFIG
            }
            pai.config.set(config_dict)

            self.llm_configured = True
            logger.info(f"PandasAI v3 LLM配置成功 - 模型: {ConfigV3.LLM_MODEL}")

            # 修复PandasAI日志中文编码问题
            self._fix_pandasai_logging()

        except Exception as e:
            logger.warning(f"LLM配置失败，将使用备用模式: {e}")
            self.llm_configured = False

    def _fix_pandasai_logging(self):
        """
        修复PandasAI日志中文编码问题
        解决pandasai.log文件中中文字符显示为乱码的问题
        """
        try:
            import logging

            # 设置系统编码环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'

            # 备份并重新创建日志文件
            log_file = 'pandasai.log'
            backup_file = 'pandasai.log.old'

            # 如果存在旧的日志文件，备份它
            if os.path.exists(log_file):
                try:
                    import shutil
                    shutil.move(log_file, backup_file)
                    logger.info(f"已备份旧日志文件为: {backup_file}")
                except Exception as backup_error:
                    logger.warning(f"备份日志文件失败: {backup_error}")

            # 获取PandasAI日志器
            pandasai_logger = logging.getLogger('pandasai')

            # 清除所有现有处理器
            for handler in pandasai_logger.handlers[:]:
                pandasai_logger.removeHandler(handler)
                if hasattr(handler, 'close'):
                    handler.close()

            # 创建新的UTF-8编码文件处理器
            utf8_handler = logging.FileHandler(log_file, encoding='utf-8', mode='w')
            utf8_handler.setFormatter(logging.Formatter(
                '%(asctime)s [%(levelname)s] %(message)s'
            ))
            utf8_handler.setLevel(logging.INFO)

            # 添加新的处理器
            pandasai_logger.addHandler(utf8_handler)
            pandasai_logger.setLevel(logging.INFO)

            # 确保不重复传播到根日志器
            pandasai_logger.propagate = False

            # 写入一条测试消息验证编码
            pandasai_logger.info("PandasAI日志编码已修复为UTF-8 - 中文测试: 数据分析引擎")

            logger.info("PandasAI日志编码已修复为UTF-8")

        except Exception as e:
            logger.warning(f"PandasAI日志编码修复失败: {e}")

    def load_data(self, df: pd.DataFrame, dataset_name: str = None,
                  dataset_type: str = "general") -> bool:
        """
        加载数据并创建语义数据集 (优化版)
        基于官方文档 /v3/getting-started#creating-your-first-data-layer
        """
        try:
            # 数据有效性检查
            if df is None:
                logger.error("传入的数据为None")
                return False

            # 修复：确保传入的是DataFrame而不是字符串
            if isinstance(df, str):
                logger.error(f"传入的数据是字符串而不是DataFrame: {df[:100]}...")
                return False

            if not isinstance(df, pd.DataFrame):
                logger.error(f"传入的数据类型错误: {type(df)}, 期望: pandas.DataFrame")
                return False

            if df.empty:
                logger.error("传入的数据为空DataFrame")
                return False

            # 保存原始数据副本 - 无论LLM是否配置都要保存数据
            try:
                self.current_data = df.copy()
                logger.info(f"数据已保存到current_data，形状: {df.shape}")
            except Exception as copy_error:
                logger.error(f"数据复制失败: {copy_error}")
                # 尝试直接赋值
                self.current_data = df
                logger.info(f"使用直接赋值保存数据，形状: {df.shape}")

            # 如果LLM未配置，只保存数据，不创建语义数据集
            if not self.llm_configured:
                logger.warning("LLM未配置，跳过语义数据集创建，使用直接DataFrame模式")
                return True

            # 如果指定了数据集名称，创建语义数据集
            if dataset_name:
                try:
                    # 验证并标准化数据集名称
                    safe_dataset_name = ConfigV3.validate_dataset_name(dataset_name)
                    dataset_path = f"financial/{safe_dataset_name}"

                    # 生成列模式和获取schema信息
                    columns_schema = self._generate_column_schema(df, dataset_type)
                    schema_info = ConfigV3.get_financial_schema(dataset_type)

                    # 验证生成的schema
                    schema_data = {
                        "name": safe_dataset_name,
                        "source": {"type": "parquet", "path": "data.parquet"},
                        "description": schema_info["description"],
                        "columns": columns_schema
                    }

                    is_valid, errors = ConfigV3.validate_schema(schema_data)
                    if not is_valid:
                        logger.warning(f"Schema验证失败: {errors}")
                        # 继续创建，但记录警告

                    # 检查数据集是否已存在，如果存在则删除重建
                    if safe_dataset_name in self.semantic_datasets:
                        logger.info(f"数据集 {safe_dataset_name} 已存在，将重新创建")
                        del self.semantic_datasets[safe_dataset_name]

                    # 检查文件系统中是否存在数据集目录，如果存在则删除
                    dataset_dir = os.path.join(ConfigV3.SEMANTIC_LAYER_PATH, "financial", safe_dataset_name)
                    if os.path.exists(dataset_dir):
                        shutil.rmtree(dataset_dir)
                        logger.info(f"已删除现有数据集目录: {dataset_dir}")

                    # 使用官方API创建语义数据集
                    pai_df = pai.DataFrame(df)

                    # 创建语义数据集，包含views配置
                    dataset = pai.create(
                        path=dataset_path,
                        df=pai_df,
                        description=schema_info["description"],
                        columns=columns_schema
                    )

                    # 创建Schema文件（使用语义层管理器）
                    success, message = self.semantic_manager.create_schema_file(
                        dataset_name, df, dataset_type
                    )
                    if success:
                        logger.info(f"Schema文件创建成功: {message}")
                    else:
                        logger.warning(f"Schema文件创建失败: {message}")

                    # 如果有views配置，尝试创建views
                    if "views" in schema_info and schema_info["views"]:
                        self._create_dataset_views(dataset, schema_info["views"], safe_dataset_name)

                    self.semantic_datasets[dataset_name] = dataset
                    logger.info(f"语义数据集创建成功: {dataset_name} -> {safe_dataset_name} (类型: {dataset_type})")

                except Exception as semantic_error:
                    logger.warning(f"语义数据集创建失败: {semantic_error}")
                    logger.info("将使用直接DataFrame模式进行分析")
                    # 不抛出异常，继续使用直接DataFrame模式

            logger.info(f"数据加载成功，形状: {df.shape}")
            return True

        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            # 即使语义数据集创建失败，也保存数据以便使用直接DataFrame
            if df is not None and isinstance(df, pd.DataFrame) and not df.empty:
                try:
                    self.current_data = df.copy()
                    logger.info("已保存数据副本，将使用直接DataFrame模式")
                    return True  # 返回True以便继续使用
                except Exception as copy_error:
                    logger.error(f"数据副本创建失败: {copy_error}")
                    return False
            else:
                logger.error("无法保存无效数据")
                return False
    
    def _generate_column_schema(self, df: pd.DataFrame, dataset_type: str) -> List[Dict]:
        """
        生成列模式定义 - 用于语义数据集 (优化版)
        基于官方文档 /v3/semantic-layer/new
        """
        columns = []

        for col in df.columns:
            dtype = str(df[col].dtype)

            # 使用配置中的类型映射
            col_type = ConfigV3.get_data_type_mapping(dtype)

            # 特殊处理日期类型
            if 'date' in col.lower() or 'time' in col.lower():
                col_type = "datetime"

            # 生成智能描述（中英文）
            description_cn = self._generate_column_description(col, dataset_type, "zh")
            description_en = self._generate_column_description(col, dataset_type, "en")

            # 数据质量分析
            quality_info = self._analyze_column_quality(df[col])

            column_def = {
                "name": col,
                "type": col_type,
                "description": description_cn,
                "description_en": description_en,
                "quality": quality_info
            }

            # 如果是数值类型，添加统计信息
            if col_type in ["integer", "float"] and df[col].notna().any():
                try:
                    column_def["stats"] = {
                        "min": float(df[col].min()),
                        "max": float(df[col].max()),
                        "mean": float(df[col].mean()),
                        "std": float(df[col].std()) if df[col].std() is not None else 0.0
                    }
                except Exception as e:
                    logger.warning(f"无法计算列 {col} 的统计信息: {e}")

            columns.append(column_def)

        return columns

    def _analyze_column_quality(self, series: pd.Series) -> Dict:
        """分析列数据质量"""
        total_count = len(series)
        null_count = series.isnull().sum()
        unique_count = series.nunique()

        return {
            "total_count": total_count,
            "null_count": int(null_count),
            "null_percentage": float(null_count / total_count * 100) if total_count > 0 else 0.0,
            "unique_count": int(unique_count),
            "unique_percentage": float(unique_count / total_count * 100) if total_count > 0 else 0.0
        }

    def _create_dataset_views(self, dataset, views_config: Dict, dataset_name: str):
        """创建数据集视图"""
        try:
            for view_name, view_config in views_config.items():
                logger.info(f"为数据集 {dataset_name} 创建视图: {view_name}")
                # 注意：这里需要根据PandasAI v3的实际API来实现
                # 当前版本可能还不完全支持views，所以先记录日志
                logger.info(f"视图配置: {view_config}")
                # 暂时不使用dataset参数，避免警告
                _ = dataset
        except Exception as e:
            logger.warning(f"创建视图失败: {e}")

    def get_semantic_datasets_info(self) -> List[Dict]:
        """获取所有语义数据集信息"""
        return self.semantic_manager.list_datasets()

    def delete_semantic_dataset(self, dataset_name: str) -> Tuple[bool, str]:
        """删除语义数据集"""
        # 从内存中删除
        if dataset_name in self.semantic_datasets:
            del self.semantic_datasets[dataset_name]

        # 从文件系统中删除
        return self.semantic_manager.delete_dataset(dataset_name)

    def get_dataset_detailed_info(self, dataset_name: str) -> Optional[Dict]:
        """获取数据集详细信息"""
        return self.semantic_manager.get_dataset_info(dataset_name)
    
    def _generate_column_description(self, column_name: str, dataset_type: str, language: str = "zh") -> str:
        """生成智能列描述 (支持中英文)"""
        col_lower = column_name.lower()

        # 财务相关字段描述映射 (中英文)
        financial_keywords = {
            'date': {'zh': '日期字段', 'en': 'Date field'},
            'time': {'zh': '时间字段', 'en': 'Time field'},
            'amount': {'zh': '金额', 'en': 'Amount'},
            'revenue': {'zh': '收入', 'en': 'Revenue'},
            'income': {'zh': '收入', 'en': 'Income'},
            'expense': {'zh': '支出', 'en': 'Expense'},
            'cost': {'zh': '成本', 'en': 'Cost'},
            'profit': {'zh': '利润', 'en': 'Profit'},
            'loss': {'zh': '损失', 'en': 'Loss'},
            'price': {'zh': '价格', 'en': 'Price'},
            'value': {'zh': '价值', 'en': 'Value'},
            'quantity': {'zh': '数量', 'en': 'Quantity'},
            'count': {'zh': '计数', 'en': 'Count'},
            'category': {'zh': '类别', 'en': 'Category'},
            'type': {'zh': '类型', 'en': 'Type'},
            'region': {'zh': '地区', 'en': 'Region'},
            'area': {'zh': '区域', 'en': 'Area'},
            'department': {'zh': '部门', 'en': 'Department'},
            'product': {'zh': '产品', 'en': 'Product'},
            'service': {'zh': '服务', 'en': 'Service'},
            'customer': {'zh': '客户', 'en': 'Customer'},
            'vendor': {'zh': '供应商', 'en': 'Vendor'},
            'balance': {'zh': '余额', 'en': 'Balance'},
            'asset': {'zh': '资产', 'en': 'Asset'},
            'liability': {'zh': '负债', 'en': 'Liability'},
            'equity': {'zh': '权益', 'en': 'Equity'}
        }

        # 查找匹配的关键词
        for keyword, desc_dict in financial_keywords.items():
            if keyword in col_lower:
                desc = desc_dict.get(language, desc_dict['zh'])
                return f"{desc} - {column_name}"

        # 根据数据集类型生成默认描述
        type_descriptions = {
            "revenue": {
                'zh': f"收入相关字段 - {column_name}",
                'en': f"Revenue-related field - {column_name}"
            },
            "expenses": {
                'zh': f"支出相关字段 - {column_name}",
                'en': f"Expense-related field - {column_name}"
            },
            "balance_sheet": {
                'zh': f"资产负债表字段 - {column_name}",
                'en': f"Balance sheet field - {column_name}"
            },
            "general": {
                'zh': f"数据字段 - {column_name}",
                'en': f"Data field - {column_name}"
            }
        }

        desc_dict = type_descriptions.get(dataset_type, type_descriptions["general"])
        return desc_dict.get(language, desc_dict['zh'])


    def _enhance_financial_query(self, question: str) -> str:
        """
        通用财务查询增强器 - 优化查询语句以提高PandasAI的理解准确性
        """
        enhanced_query = question

        # 财务术语标准化映射
        financial_terms_mapping = {
            "总收入": "营业总收入",
            "收入": "营业收入",
            "利润": "净利润",
            "成本": "营业成本",
            "费用": "期间费用"
        }

        # 数据类型标准化 - 明确指定要查询的数据类型
        if any(keyword in enhanced_query for keyword in ["对比", "比较"]) and "月" in enhanced_query:
            if "累计" not in enhanced_query and "年累计" not in enhanced_query:
                enhanced_query += " 请使用'本月数'数据类型进行对比分析，不要使用'本年累计数'。"

        # 应用财务术语标准化
        for old_term, new_term in financial_terms_mapping.items():
            if old_term in enhanced_query and new_term not in enhanced_query:
                enhanced_query = enhanced_query.replace(old_term, new_term)

        # 添加数据结构提示，帮助PandasAI更好理解财务数据
        if any(keyword in enhanced_query for keyword in ["对比", "比较", "分析"]):
            enhanced_query += """

重要提示：
1. 数据结构说明：
   - 公司：公司的完整名称
   - 年份：财务数据的年份
   - 月份：财务数据的月份
   - 财报类型：利润表、资产负债表、现金流量表等
   - 财报项目：具体的财务科目（如营业总收入、净利润等）
   - 数据类型：期初余额、期末余额、本月数、累计数等
   - 数据：具体的财务数据值

2. 查询要求：
   - 请精确筛选数据，确保公司、年份、月份、财报类型、财报项目、数据类型都匹配
   - 对于月度对比分析，请务必使用'本月数'数据类型，不要使用'本年累计数'
   - 请分别计算各个时间段的数据，不要进行任何额外的计算或聚合
   - 请提供详细的数值结果，包括绝对值和增长率
   - 确保返回的数据准确无误，直接使用筛选出的数据值

3. 输出格式：
   - 请提供清晰的对比结果
   - 包含具体的数值和计算过程
   - 如果有多个时间段，请分别列出每个时间段的数据
            """

        return enhanced_query.strip()

    def _create_financial_context(self) -> str:
        """
        创建财务数据上下文信息，帮助PandasAI更好理解数据结构
        """
        if self.current_data is None:
            return ""

        # 分析数据结构
        context_info = []

        # 获取数据基本信息
        context_info.append(f"数据集包含 {len(self.current_data)} 行数据")

        # 获取公司列表
        if '公司' in self.current_data.columns:
            companies = self.current_data['公司'].unique()
            context_info.append(f"包含公司: {', '.join(companies[:5])}{'等' if len(companies) > 5 else ''}")

        # 获取年份范围
        if '年份' in self.current_data.columns:
            years = sorted(self.current_data['年份'].unique())
            context_info.append(f"年份范围: {years[0]}-{years[-1]}")

        # 获取报表类型
        if '财报类型' in self.current_data.columns:
            report_types = self.current_data['财报类型'].unique()
            context_info.append(f"财报类型: {', '.join(report_types)}")

        # 获取主要科目
        if '财报项目' in self.current_data.columns:
            subjects = self.current_data['财报项目'].unique()
            context_info.append(f"主要财报项目: {', '.join(subjects[:10])}{'等' if len(subjects) > 10 else ''}")

        # 获取数据类型
        if '数据类型' in self.current_data.columns:
            data_types = self.current_data['数据类型'].unique()
            context_info.append(f"数据类型: {', '.join(data_types)}")

        return "数据上下文：" + "；".join(context_info)

    def analyze(self, question: str, dataset_name: str = None) -> Dict[str, Any]:
        """
        执行数据分析 - 基于官方文档 /v3/chat-and-output
        支持语义数据集和直接DataFrame两种模式
        """
        try:
            if not self.llm_configured:
                return {
                    "type": "error",
                    "content": "LLM未配置，请检查API密钥设置"
                }
            
            if self.current_data is None:
                return {
                    "type": "error", 
                    "content": "请先加载数据文件"
                }
            
            logger.info(f"开始分析问题: {question}")

            # 增强查询语句以提高准确性
            enhanced_question = self._enhance_financial_query(question)
            context_info = self._create_financial_context()

            # 构建完整的查询上下文
            full_query = f"{context_info}\n\n查询问题: {enhanced_question}"

            logger.info(f"增强后的查询: {enhanced_question}")

            # 选择分析方式
            if dataset_name and dataset_name in self.semantic_datasets:
                # 使用语义数据集 - 官方推荐方式
                dataset = self.semantic_datasets[dataset_name]
                result = dataset.chat(full_query)
                logger.info("使用语义数据集进行分析")
            else:
                # 使用直接DataFrame - 兼容模式
                pai_df = pai.DataFrame(self.current_data)
                result = pai_df.chat(full_query)
                logger.info("使用直接DataFrame进行分析")
            
            return self._format_result(result)
            
        except Exception as e:
            error_msg = f"分析失败: {str(e)}"
            logger.error(error_msg)
            
            # 提供备用分析方法
            try:
                backup_result = self._backup_analysis(question)
                return backup_result
            except:
                return {
                    "type": "error",
                    "content": error_msg
                }
    
    def _format_result(self, result: Any) -> Dict[str, Any]:
        """
        格式化v3结果 - 基于官方文档 /v3/chat-and-output#available-output-formats
        """
        try:
            # v3结果对象处理
            if hasattr(result, 'value'):
                value = result.value
            else:
                value = result
            
            # 根据类型格式化 - 遵循官方输出格式
            if isinstance(value, pd.DataFrame):
                return {
                    "type": "dataframe",
                    "content": value,
                    "metadata": {
                        "shape": value.shape,
                        "columns": value.columns.tolist()
                    }
                }
            elif isinstance(value, str):
                return {
                    "type": "text",
                    "content": value
                }
            elif isinstance(value, (int, float)):
                return {
                    "type": "number",
                    "content": value,
                    "formatted": f"{value:,.2f}" if isinstance(value, float) else f"{value:,}"
                }
            elif hasattr(value, 'savefig'):  # matplotlib figure
                return {
                    "type": "plot",
                    "content": value
                }
            else:
                return {
                    "type": "text",
                    "content": str(value)
                }
                
        except Exception as e:
            logger.error(f"结果格式化失败: {e}")
            return {
                "type": "error",
                "content": f"结果格式化失败: {str(e)}"
            }
    
    def _backup_analysis(self, question: str) -> Dict[str, Any]:
        """
        备用分析方法 - 通用财务数据查询
        当PandasAI失败时提供基本的数据分析能力
        """
        if self.current_data is None:
            return {"type": "error", "content": "没有可用数据"}

        question_lower = question.lower()
        df = self.current_data

        try:
            # 基本数据信息查询
            if any(keyword in question_lower for keyword in ['形状', 'shape', '大小', '行数', '列数']):
                return {
                    "type": "text",
                    "content": f"数据形状: {df.shape[0]} 行 × {df.shape[1]} 列"
                }
            elif any(keyword in question_lower for keyword in ['列名', 'columns', '字段']):
                return {
                    "type": "text",
                    "content": f"列名: {', '.join(df.columns.tolist())}"
                }
            elif any(keyword in question_lower for keyword in ['前', 'head', '开头']):
                n = 10
                if '5' in question: n = 5
                elif '20' in question: n = 20
                return {
                    "type": "dataframe",
                    "content": df.head(n)
                }
            elif any(keyword in question_lower for keyword in ['统计', 'describe', '描述']):
                return {
                    "type": "dataframe",
                    "content": df.describe()
                }
            # 财务数据特定查询
            elif self._is_financial_comparison_query(question):
                return self._handle_financial_comparison(question)
            else:
                # 提供更有用的错误信息和建议
                suggestions = [
                    "显示前10行数据",
                    "数据统计摘要",
                    "列名信息",
                    "特定公司的财务数据对比"
                ]
                return {
                    "type": "text",
                    "content": f"抱歉，无法处理问题：'{question}'。\n\n建议尝试：\n" +
                             "\n".join(f"• {s}" for s in suggestions)
                }
        except Exception as e:
            return {
                "type": "error",
                "content": f"备用分析失败: {str(e)}"
            }

    def _is_financial_comparison_query(self, question: str) -> bool:
        """检测是否为财务对比查询"""
        comparison_keywords = ["对比", "比较", "分析", "增长", "变化"]
        financial_keywords = ["收入", "利润", "成本", "费用", "资产", "负债"]
        time_keywords = ["年", "月", "季度"]

        return (any(k in question for k in comparison_keywords) and
                any(k in question for k in financial_keywords) and
                any(k in question for k in time_keywords))

    def _handle_financial_comparison(self, question: str) -> Dict[str, Any]:
        """处理财务对比查询的备用方法"""
        try:
            # 这里可以实现基本的财务数据筛选和对比逻辑
            # 作为PandasAI失败时的备用方案
            logger.info(f"使用备用方法处理财务对比查询: {question}")
            return {
                "type": "text",
                "content": "检测到财务对比查询，但当前AI分析服务不可用。请检查网络连接或稍后重试。\n\n" +
                          "您可以尝试更具体的查询，例如：\n" +
                          "• 显示特定公司的数据\n" +
                          "• 查看特定年份的财务数据\n" +
                          "• 筛选特定科目的数据"
            }
        except Exception as e:
            return {"type": "error", "content": f"财务对比分析失败: {str(e)}"}

    def get_available_datasets(self) -> List[str]:
        """获取可用的语义数据集列表"""
        return list(self.semantic_datasets.keys())

    def create_multi_dataset_analysis(self, question: str, dataset_names: List[str]) -> Dict[str, Any]:
        """
        多数据集分析 - 基于官方文档 /v3/chat-and-output#chat-with-multiple-dataframes
        """
        try:
            datasets = [self.semantic_datasets[name] for name in dataset_names
                       if name in self.semantic_datasets]

            if not datasets:
                return {
                    "type": "error",
                    "content": "未找到指定的数据集"
                }

            # 使用官方API进行多数据集分析
            result = pai.chat(question, *datasets)
            return self._format_result(result)

        except Exception as e:
            return {
                "type": "error",
                "content": f"多数据集分析失败: {str(e)}"
            }

    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        if self.current_data is None:
            return {
                "type": "error",
                "content": "没有加载的数据"
            }

        try:
            df = self.current_data
            summary = []

            # 基本信息
            summary.append(f"📊 **数据概览**")
            summary.append(f"- 形状: {df.shape[0]} 行 × {df.shape[1]} 列")
            summary.append(f"- 列名: {', '.join(df.columns.tolist())}")

            # 数据类型统计
            dtype_counts = df.dtypes.value_counts()
            summary.append(f"\n📋 **数据类型分布**:")
            for dtype, count in dtype_counts.items():
                summary.append(f"- {dtype}: {count} 列")

            # 缺失值统计
            null_counts = df.isnull().sum()
            total_nulls = null_counts.sum()
            if total_nulls > 0:
                summary.append(f"\n⚠️ **缺失值情况** (总计: {total_nulls}):")
                for col, count in null_counts[null_counts > 0].items():
                    pct = (count / len(df)) * 100
                    summary.append(f"- {col}: {count} 个 ({pct:.1f}%)")
            else:
                summary.append(f"\n✅ **数据质量**: 无缺失值")

            # 内存使用
            memory_mb = df.memory_usage(deep=True).sum() / 1024**2
            summary.append(f"\n💾 **内存使用**: {memory_mb:.2f} MB")

            return {
                "type": "text",
                "content": "\n".join(summary)
            }

        except Exception as e:
            return {
                "type": "error",
                "content": f"获取数据摘要失败: {str(e)}"
            }

    def validate_llm_connection(self) -> Dict[str, Any]:
        """验证LLM连接状态"""
        if not self.llm_configured:
            return {
                "type": "error",
                "content": "LLM未配置"
            }

        try:
            # 创建简单测试数据
            test_df = pd.DataFrame({"test": [1, 2, 3]})
            pai_df = pai.DataFrame(test_df)

            # 执行简单查询测试
            result = pai_df.chat("How many rows are in this data?")

            return {
                "type": "text",
                "content": f"✅ LLM连接正常，测试结果: {result}"
            }

        except Exception as e:
            return {
                "type": "error",
                "content": f"LLM连接测试失败: {str(e)}"
            }

    def clear_semantic_datasets(self):
        """清理所有语义数据集"""
        try:
            self.semantic_datasets.clear()
            logger.info("语义数据集已清理")
        except Exception as e:
            logger.error(f"清理语义数据集失败: {e}")

    def reload_data(self, df: pd.DataFrame, dataset_name: str = None,
                   dataset_type: str = "general") -> bool:
        """重新加载数据，先清理现有数据集"""
        try:
            # 清理现有数据集
            self.clear_semantic_datasets()

            # 重新加载数据
            return self.load_data(df, dataset_name, dataset_type)

        except Exception as e:
            logger.error(f"重新加载数据失败: {e}")
            return False
