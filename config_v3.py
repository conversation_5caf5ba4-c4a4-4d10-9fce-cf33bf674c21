"""
PandasAI v3 配置文件 - 严格遵循官方API规范
基于官方文档: https://docs.pandas-ai.com/v3/introduction
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 确保从正确的路径加载环境变量
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path, override=True)

class ConfigV3:
    """PandasAI v3 配置类 - 遵循官方文档规范"""
    
    # API配置（使用阿里云百炼官方环境变量名称）
    DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")
    LLM_MODEL = os.getenv("LLM_MODEL", "qwen-max")  # 修复：移除前缀，在代码中动态添加
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.1"))
    LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
    
    # PandasAI v3 全局配置 - 基于官方文档 /v3/overview-nl
    PANDASAI_CONFIG = {
        "save_logs": True,          # 保存LLM日志到pandasai.log
        "verbose": False,           # 关闭控制台详细输出，减少重复日志
        "max_retries": 3,          # 错误重试次数
        "temperature": LLM_TEMPERATURE,  # LLM温度参数
        "enable_cache": True,       # 启用缓存，提高性能
        "log_level": "WARNING"      # 设置PandasAI日志级别为WARNING，减少INFO日志
    }
    
    # 语义层配置 - 基于官方文档 /v3/semantic-layer
    SEMANTIC_LAYER_PATH = "datasets"  # 语义数据集存储路径
    
    # 应用配置
    APP_TITLE = "智能财务数据分析平台 v3.0 (PandasAI v3)"
    UPLOAD_DIRECTORY = "uploaded_files"
    EXPORTS_DIRECTORY = "exports"
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "app.log")
    
    # 支持的文件类型
    SUPPORTED_FILE_TYPES = ["csv", "xlsx", "xls"]
    
    # 财务数据语义模板 - 用于创建语义数据集 (增强版)
    FINANCIAL_SCHEMAS = {
        "revenue": {
            "description": "收入数据分析 - 包含收入相关的财务数据",
            "description_en": "Revenue Data Analysis - Contains revenue-related financial data",
            "suggested_columns": ["date", "amount", "category", "region", "product"],
            "views": {
                "monthly_revenue": {
                    "description": "月度收入汇总视图",
                    "sql": "SELECT DATE_TRUNC('month', date) as month, SUM(amount) as total_revenue FROM {table} GROUP BY month ORDER BY month"
                },
                "revenue_by_category": {
                    "description": "按类别分组的收入视图",
                    "sql": "SELECT category, SUM(amount) as total_revenue FROM {table} GROUP BY category ORDER BY total_revenue DESC"
                }
            },
            "transformations": [
                {
                    "name": "standardize_date",
                    "description": "标准化日期格式",
                    "type": "date_format"
                },
                {
                    "name": "validate_amount",
                    "description": "验证金额字段为正数",
                    "type": "validation"
                }
            ]
        },
        "expenses": {
            "description": "支出数据分析 - 包含支出和成本相关数据",
            "description_en": "Expense Data Analysis - Contains expense and cost-related data",
            "suggested_columns": ["date", "amount", "category", "department", "vendor"],
            "views": {
                "monthly_expenses": {
                    "description": "月度支出汇总视图",
                    "sql": "SELECT DATE_TRUNC('month', date) as month, SUM(amount) as total_expenses FROM {table} GROUP BY month ORDER BY month"
                },
                "expenses_by_department": {
                    "description": "按部门分组的支出视图",
                    "sql": "SELECT department, SUM(amount) as total_expenses FROM {table} GROUP BY department ORDER BY total_expenses DESC"
                }
            },
            "transformations": [
                {
                    "name": "standardize_date",
                    "description": "标准化日期格式",
                    "type": "date_format"
                },
                {
                    "name": "validate_amount",
                    "description": "验证金额字段为正数",
                    "type": "validation"
                }
            ]
        },
        "balance_sheet": {
            "description": "资产负债表数据 - 包含资产、负债和权益数据",
            "description_en": "Balance Sheet Data - Contains assets, liabilities and equity data",
            "suggested_columns": ["date", "assets", "liabilities", "equity"],
            "views": {
                "financial_ratios": {
                    "description": "财务比率计算视图",
                    "sql": "SELECT date, assets, liabilities, equity, (liabilities/assets) as debt_ratio, (equity/assets) as equity_ratio FROM {table} ORDER BY date"
                },
                "quarterly_balance": {
                    "description": "季度资产负债表视图",
                    "sql": "SELECT DATE_TRUNC('quarter', date) as quarter, AVG(assets) as avg_assets, AVG(liabilities) as avg_liabilities, AVG(equity) as avg_equity FROM {table} GROUP BY quarter ORDER BY quarter"
                }
            },
            "transformations": [
                {
                    "name": "standardize_date",
                    "description": "标准化日期格式",
                    "type": "date_format"
                },
                {
                    "name": "calculate_ratios",
                    "description": "计算财务比率",
                    "type": "calculation"
                }
            ]
        },
        "general": {
            "description": "通用财务数据分析",
            "description_en": "General Financial Data Analysis",
            "suggested_columns": [],
            "views": {},
            "transformations": [
                {
                    "name": "standardize_date",
                    "description": "标准化日期格式",
                    "type": "date_format"
                }
            ]
        }
    }

    # 数据类型映射 - 支持更多PandasAI v3类型
    DATA_TYPE_MAPPING = {
        'int64': 'integer',
        'int32': 'integer',
        'int16': 'integer',
        'int8': 'integer',
        'float64': 'float',
        'float32': 'float',
        'object': 'string',
        'string': 'string',
        'datetime64[ns]': 'datetime',
        'datetime64': 'datetime',
        'bool': 'boolean',
        'category': 'string'
    }

    # Schema验证规则
    SCHEMA_VALIDATION_RULES = {
        "required_fields": ["name", "source", "description", "columns"],
        "column_required_fields": ["name", "type", "description"],
        "supported_types": ["integer", "float", "string", "datetime", "boolean"],
        "max_description_length": 500,
        "min_description_length": 10
    }
    
    # 引导话术
    GUIDANCE_MESSAGE = """
    我是基于PandasAI v3的智能数据分析助手，专门帮助您分析财务数据。
    
    v3新功能特性：
    - 🧠 语义数据层：更智能的数据理解
    - 🚀 增强AI分析：更准确的自然语言处理
    - 📊 多种输出格式：文本、图表、数据表格
    - 🎯 多数据集支持：跨数据集分析能力
    
    请提出与您上传数据相关的问题，例如：
    - "显示前10行数据"
    - "哪个产品的销售额最高？"
    - "按月份绘制销售趋势图"
    - "计算各类别的平均值"
    - "找出异常值"
    """
    
    @classmethod
    def validate_config(cls):
        """验证配置是否完整"""
        if not cls.DASHSCOPE_API_KEY:
            raise ValueError("DASHSCOPE_API_KEY 未设置，请在.env文件中配置您的API密钥")
        
        # 创建必要目录
        directories = [
            cls.UPLOAD_DIRECTORY, 
            cls.EXPORTS_DIRECTORY, 
            cls.SEMANTIC_LAYER_PATH,
            f"{cls.EXPORTS_DIRECTORY}/charts"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                
        return True
    
    @classmethod
    def get_financial_schema(cls, dataset_type: str) -> dict:
        """获取财务数据模式"""
        return cls.FINANCIAL_SCHEMAS.get(dataset_type, cls.FINANCIAL_SCHEMAS["general"])

    @classmethod
    def validate_dataset_name(cls, name: str) -> str:
        """
        验证并标准化数据集名称
        确保符合PandasAI v3规范：小写+连字符
        """
        if not name:
            raise ValueError("数据集名称不能为空")

        # 转换为小写并替换特殊字符
        safe_name = name.lower()
        safe_name = safe_name.replace('_', '-').replace(' ', '-')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c == '-')

        # 移除连续的连字符
        while '--' in safe_name:
            safe_name = safe_name.replace('--', '-')

        # 移除开头和结尾的连字符
        safe_name = safe_name.strip('-')

        if not safe_name:
            raise ValueError("数据集名称无效，请使用字母数字字符")

        return safe_name

    @classmethod
    def validate_schema(cls, schema_data: dict) -> tuple[bool, list]:
        """
        验证Schema数据格式
        返回: (是否有效, 错误列表)
        """
        errors = []
        rules = cls.SCHEMA_VALIDATION_RULES

        # 检查必需字段
        for field in rules["required_fields"]:
            if field not in schema_data:
                errors.append(f"缺少必需字段: {field}")

        # 检查描述长度
        if "description" in schema_data:
            desc_len = len(schema_data["description"])
            if desc_len < rules["min_description_length"]:
                errors.append(f"描述过短，至少需要{rules['min_description_length']}个字符")
            elif desc_len > rules["max_description_length"]:
                errors.append(f"描述过长，最多{rules['max_description_length']}个字符")

        # 检查列定义
        if "columns" in schema_data and isinstance(schema_data["columns"], list):
            for i, col in enumerate(schema_data["columns"]):
                if not isinstance(col, dict):
                    errors.append(f"列{i+1}定义格式错误")
                    continue

                # 检查列的必需字段
                for field in rules["column_required_fields"]:
                    if field not in col:
                        errors.append(f"列{i+1}缺少必需字段: {field}")

                # 检查数据类型
                if "type" in col and col["type"] not in rules["supported_types"]:
                    errors.append(f"列{i+1}使用了不支持的数据类型: {col['type']}")

        return len(errors) == 0, errors

    @classmethod
    def get_data_type_mapping(cls, pandas_dtype: str) -> str:
        """
        获取Pandas数据类型到PandasAI v3类型的映射
        """
        return cls.DATA_TYPE_MAPPING.get(str(pandas_dtype), "string")
