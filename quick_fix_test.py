#!/usr/bin/env python3
"""
快速修复验证脚本
解决 ModuleNotFoundError: No module named 'pandasai_litellm' 问题
"""
import sys
import os
import subprocess

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查虚拟环境
    venv = os.environ.get('VIRTUAL_ENV')
    if venv:
        print(f"✅ 虚拟环境: {venv}")
    else:
        print("⚠️ 未检测到虚拟环境")
    
    return True

def test_critical_imports():
    """测试关键模块导入"""
    print("\n🧪 测试关键模块导入...")
    
    tests = [
        ("pandas", "import pandas as pd"),
        ("pandasai", "import pandasai as pai"),
        ("pandasai_litellm", "import pandasai_litellm"),
        ("LiteLLM", "from pandasai_litellm.litellm import LiteLLM"),
        ("streamlit", "import streamlit as st"),
        ("config_v3", "from config_v3 import ConfigV3"),
        ("analysis_engine", "from core.analysis_engine_v3 import AnalysisEngineV3")
    ]
    
    results = []
    for name, import_stmt in tests:
        try:
            exec(import_stmt)
            print(f"✅ {name}: 导入成功")
            results.append((name, True))
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results.append((name, False))
        except Exception as e:
            print(f"⚠️ {name}: 其他错误 - {e}")
            results.append((name, False))
    
    return results

def check_package_installation():
    """检查包安装状态"""
    print("\n📦 检查包安装状态...")
    
    packages = [
        "pandasai",
        "pandasai-litellm", 
        "streamlit",
        "pandas"
    ]
    
    for package in packages:
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", package],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                version_line = next((line for line in lines if line.startswith('Version:')), None)
                location_line = next((line for line in lines if line.startswith('Location:')), None)
                
                version = version_line.split(': ')[1] if version_line else "未知"
                location = location_line.split(': ')[1] if location_line else "未知"
                
                print(f"✅ {package}: v{version}")
                print(f"   位置: {location}")
            else:
                print(f"❌ {package}: 未安装")
                
        except Exception as e:
            print(f"⚠️ {package}: 检查失败 - {e}")

def test_pandasai_functionality():
    """测试PandasAI基本功能"""
    print("\n🤖 测试PandasAI基本功能...")
    
    try:
        import pandas as pd
        import pandasai as pai
        from pandasai_litellm.litellm import LiteLLM
        
        # 创建测试数据
        df = pd.DataFrame({
            'name': ['Alice', 'Bob'],
            'age': [25, 30]
        })
        
        # 创建PandasAI DataFrame
        pai_df = pai.DataFrame(df)
        print("✅ PandasAI DataFrame创建成功")
        
        # 测试LiteLLM实例创建
        try:
            llm = LiteLLM(
                model="dashscope/qwen-max",
                temperature=0.1,
                max_tokens=100,
                api_key="test_key"  # 测试用密钥
            )
            print("✅ LiteLLM实例创建成功")
        except Exception as e:
            print(f"⚠️ LiteLLM实例创建失败（可能是API密钥问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ PandasAI功能测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n🛠️ 解决方案建议:")
    print("=" * 60)
    
    print("1. 重新激活虚拟环境:")
    print("   Windows: venv\\Scripts\\activate")
    print("   Linux/Mac: source venv/bin/activate")
    
    print("\n2. 验证Python路径:")
    print("   python -c \"import sys; print(sys.executable)\"")
    
    print("\n3. 重新安装依赖:")
    print("   pip install -r requirements.txt")
    
    print("\n4. 清理缓存:")
    print("   python -c \"import importlib; importlib.invalidate_caches()\"")
    
    print("\n5. 测试导入:")
    print("   python -c \"from pandasai_litellm.litellm import LiteLLM; print('Success')\"")
    
    print("\n6. 启动应用:")
    print("   streamlit run app_v3.py")

def main():
    """主函数"""
    print("🚀 ModuleNotFoundError 快速修复验证")
    print("=" * 80)
    
    # 检查环境
    check_python_environment()
    
    # 检查包安装
    check_package_installation()
    
    # 测试导入
    import_results = test_critical_imports()
    
    # 测试功能
    functionality_ok = test_pandasai_functionality()
    
    # 分析结果
    print("\n" + "=" * 80)
    print("📊 诊断结果")
    print("=" * 80)
    
    failed_imports = [name for name, success in import_results if not success]
    
    if not failed_imports and functionality_ok:
        print("🎉 所有测试通过！")
        print("✅ 环境配置正确，可以正常运行 streamlit run app_v3.py")
    elif failed_imports:
        print(f"❌ 以下模块导入失败: {', '.join(failed_imports)}")
        print("🔧 需要修复导入问题")
        provide_solutions()
    else:
        print("⚠️ 导入正常但功能测试失败")
        print("🔧 可能需要检查API配置")
        provide_solutions()
    
    return len(failed_imports) == 0 and functionality_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
