#!/usr/bin/env python3
"""
测试PandasAI中文日志编码修复
验证pandasai.log文件中的中文字符是否正确显示
"""
import pandas as pd
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def test_chinese_logging_before_fix():
    """测试修复前的中文日志编码"""
    print("🔍 测试修复前的中文日志编码...")
    
    # 检查现有的pandasai.log文件
    if os.path.exists('pandasai.log'):
        try:
            with open('pandasai.log', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找中文乱码
            if '���' in content:
                print("❌ 发现中文乱码，需要修复")
                
                # 显示一些乱码示例
                lines = content.split('\n')
                garbled_lines = [line for line in lines if '���' in line]
                
                print("📋 乱码示例:")
                for i, line in enumerate(garbled_lines[:5]):  # 只显示前5行
                    print(f"   {i+1}. {line[:100]}...")
                
                return False
            else:
                print("✅ 未发现中文乱码")
                return True
                
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
            return False
    else:
        print("⚠️ pandasai.log文件不存在")
        return True

def test_chinese_logging_after_fix():
    """测试修复后的中文日志编码"""
    print("\n🔧 测试修复后的中文日志编码...")
    
    try:
        # 导入修复后的分析引擎
        from core.analysis_engine_v3 import AnalysisEngineV3
        
        # 创建分析引擎实例（会自动修复日志编码）
        engine = AnalysisEngineV3()
        
        # 加载环境变量
        load_dotenv()
        api_key = os.getenv('DASHSCOPE_API_KEY')
        
        if not api_key:
            print("⚠️ 未找到DASHSCOPE_API_KEY，跳过实际API测试")
            print("✅ 日志编码修复代码已执行")
            return True
        
        # 创建中文测试数据
        df = pd.DataFrame({
            '公司名称': ['中建集团', '九州铁建', '远大建设'],
            '营业收入': [1000000, 2000000, 1500000],
            '年份': [2023, 2023, 2023]
        })
        
        print("📊 中文测试数据:")
        print(df)
        
        # 加载数据到分析引擎
        success = engine.load_data(df, "中文测试数据集", "financial")
        
        if success:
            print("✅ 中文数据加载成功")
            
            # 执行中文查询
            question = "显示所有公司的营业收入数据"
            print(f"\n🤖 测试中文查询: {question}")
            
            try:
                result = engine.analyze(question)
                print(f"✅ 中文查询执行成功")
                print(f"结果类型: {result.get('type', 'unknown')}")
                
                # 检查日志文件
                return check_log_encoding()
                
            except Exception as e:
                print(f"⚠️ 查询执行失败（可能是API问题）: {e}")
                # 即使查询失败，也检查日志编码
                return check_log_encoding()
        else:
            print("❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_log_encoding():
    """检查日志文件编码"""
    print("\n📋 检查pandasai.log文件编码...")
    
    if not os.path.exists('pandasai.log'):
        print("⚠️ pandasai.log文件不存在")
        return True
    
    try:
        with open('pandasai.log', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找最近的中文内容
        lines = content.split('\n')
        recent_lines = lines[-50:]  # 检查最近50行
        
        chinese_lines = []
        garbled_lines = []
        
        for line in recent_lines:
            if any('\u4e00' <= char <= '\u9fff' for char in line):  # 检查中文字符
                chinese_lines.append(line)
            elif '���' in line:
                garbled_lines.append(line)
        
        print(f"📊 检查结果:")
        print(f"   最近50行中包含中文的行数: {len(chinese_lines)}")
        print(f"   包含乱码的行数: {len(garbled_lines)}")
        
        if garbled_lines:
            print("\n❌ 仍然存在中文乱码:")
            for i, line in enumerate(garbled_lines[:3]):
                print(f"   {i+1}. {line[:100]}...")
            return False
        elif chinese_lines:
            print("\n✅ 中文显示正常:")
            for i, line in enumerate(chinese_lines[:3]):
                print(f"   {i+1}. {line[:100]}...")
            return True
        else:
            print("\n⚠️ 未找到中文内容，无法验证编码")
            return True
            
    except UnicodeDecodeError as e:
        print(f"❌ 文件编码错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def backup_log_file():
    """备份现有日志文件"""
    if os.path.exists('pandasai.log'):
        backup_name = 'pandasai.log.backup'
        try:
            import shutil
            shutil.copy2('pandasai.log', backup_name)
            print(f"✅ 已备份日志文件为: {backup_name}")
        except Exception as e:
            print(f"⚠️ 备份失败: {e}")

def restore_log_file():
    """恢复日志文件"""
    backup_name = 'pandasai.log.backup'
    if os.path.exists(backup_name):
        try:
            import shutil
            shutil.copy2(backup_name, 'pandasai.log')
            print(f"✅ 已恢复日志文件")
        except Exception as e:
            print(f"⚠️ 恢复失败: {e}")

def main():
    """主测试函数"""
    print("🧪 PandasAI中文日志编码修复测试")
    print("=" * 80)
    
    # 备份现有日志文件
    backup_log_file()
    
    try:
        # 测试修复前的状态
        before_fix = test_chinese_logging_before_fix()
        
        # 测试修复后的状态
        after_fix = test_chinese_logging_after_fix()
        
        # 总结
        print("\n" + "=" * 80)
        print("📊 测试总结")
        print("=" * 80)
        
        if before_fix and after_fix:
            print("✅ 中文日志编码正常，无需修复")
        elif not before_fix and after_fix:
            print("🎉 中文日志编码修复成功！")
        elif not before_fix and not after_fix:
            print("❌ 中文日志编码修复失败，需要进一步调试")
        else:
            print("⚠️ 测试结果不一致，请检查")
        
        print("\n🔧 故障排除建议:")
        print("1. 确保Python环境支持UTF-8编码")
        print("2. 检查系统环境变量PYTHONIOENCODING")
        print("3. 验证PandasAI库版本是否最新")
        print("4. 重启应用程序以确保编码设置生效")
        
        return after_fix
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
