#!/usr/bin/env python3
"""
测试日志优化效果
验证重复日志问题是否已解决
"""
import pandas as pd
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from core.data_manager import DataManager
from utils.logger import setup_logger, get_logger, reset_logger

def test_single_logger_initialization():
    """测试单次日志器初始化"""
    print("🔧 测试单次日志器初始化...")
    
    # 重置日志器
    reset_logger()
    
    # 多次调用setup_logger，应该只初始化一次
    logger1 = setup_logger()
    logger2 = setup_logger()
    logger3 = setup_logger()
    
    # 检查是否是同一个实例
    if logger1 is logger2 is logger3:
        print("✅ 多次调用setup_logger返回同一实例")
        return True
    else:
        print("❌ 多次调用setup_logger返回不同实例")
        return False

def test_no_duplicate_handlers():
    """测试没有重复的处理器"""
    print("🔧 测试处理器重复问题...")
    
    # 重置日志器
    reset_logger()
    
    # 多次初始化
    for i in range(3):
        setup_logger()
    
    # 检查根日志器的处理器数量
    import logging
    root_logger = logging.getLogger()
    handler_count = len(root_logger.handlers)
    
    print(f"📊 根日志器处理器数量: {handler_count}")
    
    # 应该只有2个处理器：文件处理器和控制台处理器
    if handler_count == 2:
        print("✅ 处理器数量正确，无重复")
        return True
    else:
        print(f"❌ 处理器数量异常: {handler_count}，期望: 2")
        return False

def test_log_output_uniqueness():
    """测试日志输出唯一性"""
    print("🔧 测试日志输出唯一性...")
    
    # 重置日志器并清空日志文件
    reset_logger()
    
    # 清空现有日志文件
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'w', encoding='utf-8') as f:
            f.write("")
    
    # 初始化日志器
    setup_logger()
    logger = get_logger(__name__)
    
    # 记录测试消息
    test_message = f"测试消息 - {time.time()}"
    logger.info(test_message)
    
    # 等待一下确保日志写入
    time.sleep(0.1)
    
    # 读取日志文件，检查重复
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 计算测试消息出现次数
        count = log_content.count(test_message)
        print(f"📊 测试消息在日志文件中出现次数: {count}")
        
        if count == 1:
            print("✅ 日志消息唯一，无重复")
            return True
        else:
            print(f"❌ 日志消息重复: {count} 次")
            return False
    else:
        print("❌ 日志文件不存在")
        return False

def test_third_party_log_levels():
    """测试第三方库日志级别设置"""
    print("🔧 测试第三方库日志级别...")
    
    import logging
    
    # 检查第三方库日志级别
    third_party_loggers = {
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'matplotlib': logging.WARNING,
        'PIL': logging.WARNING,
        'pandasai': logging.INFO,
        'litellm': logging.WARNING
    }
    
    all_correct = True
    for logger_name, expected_level in third_party_loggers.items():
        logger = logging.getLogger(logger_name)
        actual_level = logger.level
        
        if actual_level == expected_level:
            print(f"✅ {logger_name}: {logging.getLevelName(actual_level)}")
        else:
            print(f"❌ {logger_name}: 期望 {logging.getLevelName(expected_level)}, 实际 {logging.getLevelName(actual_level)}")
            all_correct = False
    
    return all_correct

def test_analysis_engine_logging():
    """测试分析引擎日志"""
    print("🔧 测试分析引擎日志...")
    
    # 重置并清空日志
    reset_logger()
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'w', encoding='utf-8') as f:
            f.write("")
    
    try:
        # 创建分析引擎（这会触发LLM配置日志）
        engine = AnalysisEngineV3()
        
        # 等待日志写入
        time.sleep(0.2)
        
        # 检查日志文件中LLM配置消息的重复情况
        if os.path.exists(ConfigV3.LOG_FILE):
            with open(ConfigV3.LOG_FILE, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # 查找LLM配置成功的消息
            llm_config_messages = [line for line in log_content.split('\n') 
                                 if 'PandasAI v3 LLM配置成功' in line]
            
            print(f"📊 LLM配置日志消息数量: {len(llm_config_messages)}")
            
            if len(llm_config_messages) <= 1:
                print("✅ LLM配置日志无重复")
                return True
            else:
                print(f"❌ LLM配置日志重复: {len(llm_config_messages)} 次")
                return False
        else:
            print("⚠️ 日志文件不存在，可能LLM配置失败")
            return True  # 如果LLM配置失败，不算日志重复问题
            
    except Exception as e:
        print(f"⚠️ 分析引擎创建失败: {e}")
        return True  # 如果创建失败，不算日志重复问题

def test_data_loading_logging():
    """测试数据加载日志"""
    print("🔧 测试数据加载日志...")
    
    # 重置并清空日志
    reset_logger()
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'w', encoding='utf-8') as f:
            f.write("")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02'],
            'amount': [1000, 2000]
        })
        
        # 创建分析引擎并加载数据
        engine = AnalysisEngineV3()
        engine.load_data(test_data, "test-data", "general")
        
        # 等待日志写入
        time.sleep(0.2)
        
        # 检查数据加载日志的重复情况
        if os.path.exists(ConfigV3.LOG_FILE):
            with open(ConfigV3.LOG_FILE, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # 查找数据加载成功的消息
            data_load_messages = [line for line in log_content.split('\n') 
                                if '数据加载成功，形状:' in line]
            
            print(f"📊 数据加载日志消息数量: {len(data_load_messages)}")
            
            if len(data_load_messages) <= 1:
                print("✅ 数据加载日志无重复")
                return True
            else:
                print(f"❌ 数据加载日志重复: {len(data_load_messages)} 次")
                return False
        else:
            print("⚠️ 日志文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试日志优化效果")
    print("=" * 60)
    
    tests = [
        ("单次日志器初始化", test_single_logger_initialization),
        ("处理器重复检查", test_no_duplicate_handlers),
        ("日志输出唯一性", test_log_output_uniqueness),
        ("第三方库日志级别", test_third_party_log_levels),
        ("分析引擎日志", test_analysis_engine_logging),
        ("数据加载日志", test_data_loading_logging)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = False
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📋 日志优化测试总结")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！日志优化成功。")
        return True
    else:
        print("💥 部分测试失败，需要进一步优化。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
