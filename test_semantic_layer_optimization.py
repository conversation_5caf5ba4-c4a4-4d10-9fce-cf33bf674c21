"""
测试语义层优化功能
"""
import pandas as pd
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_v3 import ConfigV3
from core.semantic_layer_manager import SemanticLayerManager
from core.analysis_engine_v3 import AnalysisEngineV3
from utils.logger import get_logger

logger = get_logger(__name__)

def create_test_data():
    """创建测试数据"""
    # 创建收入数据
    dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
    revenue_data = pd.DataFrame({
        '日期': dates,
        '收入': [10000 + i * 100 for i in range(30)],
        '类别': ['产品A', '产品B', '服务C'] * 10,
        '地区': ['北京', '上海', '广州', '深圳'] * 7 + ['北京', '上海']
    })
    
    # 创建支出数据
    expense_data = pd.DataFrame({
        '日期': dates,
        '支出': [5000 + i * 50 for i in range(30)],
        '部门': ['销售部', '技术部', '市场部'] * 10,
        '类别': ['人力成本', '设备费用', '营销费用'] * 10
    })
    
    return revenue_data, expense_data

def test_config_validation():
    """测试配置验证功能"""
    print("\n" + "="*60)
    print("🔧 测试配置验证功能")
    print("="*60)
    
    try:
        # 测试数据集名称验证
        test_names = [
            "Revenue Data 2024",
            "expense_report",
            "balance-sheet",
            "测试数据集",
            "Test__Data--Set",
            ""
        ]
        
        for name in test_names:
            try:
                safe_name = ConfigV3.validate_dataset_name(name)
                print(f"✅ '{name}' -> '{safe_name}'")
            except ValueError as e:
                print(f"❌ '{name}' -> 错误: {e}")
        
        # 测试Schema验证
        test_schema = {
            "name": "test-dataset",
            "source": {"type": "parquet", "path": "data.parquet"},
            "description": "这是一个测试数据集，用于验证Schema功能",
            "columns": [
                {"name": "date", "type": "datetime", "description": "日期字段"},
                {"name": "amount", "type": "integer", "description": "金额字段"},
                {"name": "category", "type": "string", "description": "类别字段"}
            ]
        }
        
        is_valid, errors = ConfigV3.validate_schema(test_schema)
        if is_valid:
            print("✅ Schema验证通过")
        else:
            print(f"❌ Schema验证失败: {errors}")
        
        # 测试数据类型映射
        test_types = ['int64', 'float64', 'object', 'datetime64[ns]', 'bool']
        for dtype in test_types:
            mapped_type = ConfigV3.get_data_type_mapping(dtype)
            print(f"📊 {dtype} -> {mapped_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False

def test_semantic_layer_manager():
    """测试语义层管理器"""
    print("\n" + "="*60)
    print("🗂️ 测试语义层管理器")
    print("="*60)
    
    try:
        manager = SemanticLayerManager()
        revenue_data, expense_data = create_test_data()
        
        # 测试创建语义数据集
        print("📝 创建收入数据集...")
        success, message = manager.create_schema_file(
            "revenue-test-2024", revenue_data, "revenue", 
            "2024年收入测试数据集"
        )
        print(f"{'✅' if success else '❌'} {message}")
        
        print("📝 创建支出数据集...")
        success, message = manager.create_schema_file(
            "expense-test-2024", expense_data, "expenses",
            "2024年支出测试数据集"
        )
        print(f"{'✅' if success else '❌'} {message}")
        
        # 测试列出数据集
        print("\n📋 列出所有数据集:")
        datasets = manager.list_datasets()
        for dataset in datasets:
            print(f"  📊 {dataset['name']}: {dataset['description']}")
            print(f"     列数: {dataset['columns']}, 路径: {dataset['path']}")
        
        # 测试获取数据集详细信息
        if datasets:
            dataset_name = datasets[0]['name']
            print(f"\n🔍 获取数据集详细信息: {dataset_name}")
            info = manager.get_dataset_info(dataset_name)
            if info:
                print(f"  Schema列数: {len(info.get('schema', {}).get('columns', []))}")
                print(f"  元数据: {info.get('metadata', {}).get('rows', 0)} 行")
        
        return True
        
    except Exception as e:
        print(f"❌ 语义层管理器测试失败: {e}")
        return False

def test_analysis_engine_integration():
    """测试分析引擎集成"""
    print("\n" + "="*60)
    print("🔬 测试分析引擎集成")
    print("="*60)
    
    try:
        engine = AnalysisEngineV3()
        revenue_data, expense_data = create_test_data()
        
        # 测试数据加载
        print("📊 加载收入数据...")
        success = engine.load_data(revenue_data, "revenue-integration-test", "revenue")
        print(f"{'✅' if success else '❌'} 数据加载: {success}")
        
        # 测试获取语义数据集信息
        print("\n📋 获取语义数据集信息:")
        datasets_info = engine.get_semantic_datasets_info()
        for dataset in datasets_info:
            print(f"  📊 {dataset['name']}: {dataset.get('metadata', {}).get('rows', 0)} 行")
        
        # 测试数据分析（如果LLM配置正确）
        if engine.llm_configured:
            print("\n🤖 测试AI分析...")
            try:
                result = engine.analyze("显示前5行数据")
                print(f"✅ 分析结果类型: {result.get('type', 'unknown')}")
            except Exception as e:
                print(f"⚠️ AI分析跳过（可能是LLM配置问题）: {e}")
        else:
            print("⚠️ LLM未配置，跳过AI分析测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析引擎集成测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n" + "="*60)
    print("🛡️ 测试错误处理")
    print("="*60)
    
    try:
        engine = AnalysisEngineV3()
        
        # 测试无效数据
        print("📊 测试无效数据处理...")
        
        # 测试None数据
        success = engine.load_data(None, "test-none")
        print(f"{'✅' if not success else '❌'} None数据处理: {not success}")
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        success = engine.load_data(empty_df, "test-empty")
        print(f"{'✅' if not success else '❌'} 空DataFrame处理: {not success}")
        
        # 测试字符串数据（错误类型）
        try:
            success = engine.load_data("invalid_data", "test-string")
            print(f"{'✅' if not success else '❌'} 字符串数据处理: {not success}")
        except Exception:
            print("✅ 字符串数据处理: 正确抛出异常")
        
        # 测试无效数据集名称
        manager = SemanticLayerManager()
        test_df = pd.DataFrame({'test': [1, 2, 3]})
        
        success, message = manager.create_schema_file("", test_df)
        print(f"{'✅' if not success else '❌'} 空名称处理: {not success}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n" + "="*60)
    print("🧹 清理测试数据")
    print("="*60)
    
    try:
        manager = SemanticLayerManager()
        datasets = manager.list_datasets()
        
        test_datasets = [d for d in datasets if 'test' in d['name'].lower()]
        
        for dataset in test_datasets:
            success, message = manager.delete_dataset(dataset['name'])
            print(f"{'✅' if success else '❌'} 删除 {dataset['name']}: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始语义层优化功能测试")
    print("="*80)
    
    # 确保配置有效
    try:
        ConfigV3.validate_config()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return
    
    # 运行测试
    tests = [
        ("配置验证", test_config_validation),
        ("语义层管理器", test_semantic_layer_manager),
        ("分析引擎集成", test_analysis_engine_integration),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 清理测试数据
    cleanup_test_data()
    
    # 总结结果
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！语义层优化功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
