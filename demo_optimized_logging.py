#!/usr/bin/env python3
"""
演示优化后的日志系统
展示无重复日志的效果
"""
import pandas as pd
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from core.data_manager import DataManager
from utils.logger import setup_logger, get_logger, reset_logger

def demo_clean_logging():
    """演示清洁的日志输出"""
    print("🎯 演示优化后的日志系统")
    print("=" * 50)
    
    # 重置并清空日志文件
    reset_logger()
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'w', encoding='utf-8') as f:
            f.write("")
    
    print("📝 开始记录日志...")
    
    # 初始化日志系统
    setup_logger()
    logger = get_logger(__name__)
    
    # 记录一些测试日志
    logger.info("应用启动")
    logger.info("配置加载完成")
    
    # 创建分析引擎（会产生LLM配置日志）
    print("🤖 创建分析引擎...")
    try:
        engine = AnalysisEngineV3()
        logger.info("分析引擎创建成功")
    except Exception as e:
        logger.warning(f"分析引擎创建失败: {e}")
    
    # 创建测试数据并加载
    print("📊 加载测试数据...")
    test_data = pd.DataFrame({
        'date': pd.date_range('2024-01-01', periods=5),
        'sales': [1000, 1200, 1100, 1300, 1250],
        'product': ['A', 'B', 'A', 'B', 'A']
    })
    
    try:
        engine.load_data(test_data, "demo-data", "general")
        logger.info("测试数据加载成功")
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
    
    # 等待日志写入
    time.sleep(0.5)
    
    # 显示日志文件内容
    print("\n📋 日志文件内容:")
    print("-" * 50)
    
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        if log_content.strip():
            print(log_content)
        else:
            print("日志文件为空")
    else:
        print("日志文件不存在")
    
    print("-" * 50)

def demo_multiple_modules():
    """演示多个模块使用日志的情况"""
    print("\n🔄 演示多模块日志使用")
    print("=" * 50)
    
    # 模拟多个模块获取日志器
    modules = ['module1', 'module2', 'module3', 'core.engine', 'utils.helper']
    
    loggers = {}
    for module_name in modules:
        loggers[module_name] = get_logger(module_name)
        loggers[module_name].info(f"{module_name} 模块初始化")
    
    print("✅ 多个模块日志记录完成")
    
    # 等待日志写入
    time.sleep(0.2)
    
    # 检查日志文件中的重复情况
    if os.path.exists(ConfigV3.LOG_FILE):
        with open(ConfigV3.LOG_FILE, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        print(f"📊 日志文件总行数: {len(log_lines)}")
        
        # 检查是否有重复的日志行
        unique_lines = set(log_lines)
        if len(unique_lines) == len(log_lines):
            print("✅ 所有日志行都是唯一的，无重复")
        else:
            print(f"❌ 发现重复日志行: {len(log_lines) - len(unique_lines)} 行")

def demo_performance_comparison():
    """演示性能对比"""
    print("\n⚡ 演示日志性能")
    print("=" * 50)
    
    # 测试大量日志记录的性能
    logger = get_logger("performance_test")
    
    start_time = time.time()
    
    # 记录1000条日志
    for i in range(1000):
        logger.info(f"性能测试日志 {i}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"📊 记录1000条日志耗时: {duration:.3f} 秒")
    print(f"📊 平均每条日志耗时: {duration/1000*1000:.3f} 毫秒")
    
    # 检查日志文件大小
    if os.path.exists(ConfigV3.LOG_FILE):
        file_size = os.path.getsize(ConfigV3.LOG_FILE)
        print(f"📊 日志文件大小: {file_size/1024:.2f} KB")

def demo_log_levels():
    """演示不同日志级别"""
    print("\n📊 演示日志级别控制")
    print("=" * 50)
    
    logger = get_logger("level_demo")
    
    # 记录不同级别的日志
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    logger.critical("这是严重错误信息")
    
    print("✅ 不同级别日志记录完成")
    print(f"📊 当前日志级别: {ConfigV3.LOG_LEVEL}")
    print("💡 只有达到设定级别的日志才会被记录")

def main():
    """主演示函数"""
    print("🎬 日志优化效果演示")
    print("=" * 60)
    
    # 演示清洁的日志输出
    demo_clean_logging()
    
    # 演示多模块使用
    demo_multiple_modules()
    
    # 演示性能
    demo_performance_comparison()
    
    # 演示日志级别
    demo_log_levels()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("📋 主要改进:")
    print("  ✅ 消除了重复日志输出")
    print("  ✅ 优化了日志器初始化")
    print("  ✅ 控制了第三方库日志噪音")
    print("  ✅ 提升了日志记录性能")
    print("  ✅ 改善了日志可读性")
    
    print(f"\n📁 查看完整日志: {ConfigV3.LOG_FILE}")
    print(f"📁 查看PandasAI日志: pandasai.log")

if __name__ == "__main__":
    main()
